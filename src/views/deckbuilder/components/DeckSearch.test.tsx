import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { DeckSearch } from './DeckSearch';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockQuery = vi.mocked(DeckActions.query);

// Mock FilterRegex
vi.mock('../../../helpers/filter_regex', () => ({
  matchColors: /c:([^"\s]+|"[^"]*")/g,
  extractFilters: vi.fn((query: string, regex: RegExp, include: boolean) => {
    if (include) {
      return ['white', 'blue'];
    }
    return ['red'];
  }),
}));

// Mock reduceToFilterState
vi.mock('../../../models/filters/FilterState', async () => {
  const actual = await vi.importActual('../../../models/filters/FilterState');
  return {
    ...actual,
    reduceToFilterState: vi.fn(() => actual.FilterState.INCLUDE),
  };
});

type DeckSearchProps = React.ComponentProps<typeof DeckSearch>;

const defaultProps: Omit<DeckSearchProps, 'dispatcher'> = {
  query: '',
};

const renderDeckSearch = (props: Partial<DeckSearchProps> = {}) => {
  return renderWithDispatcher(DeckSearch, { ...defaultProps, ...props });
};

describe('DeckSearch', () => {
  beforeEach(() => {
    mockQuery.mockClear();
  });

  describe('when component renders', () => {
    it('displays the search form', () => {
      const { container } = renderDeckSearch();
      expect(container.querySelector('.deck-search__form')).toBeInTheDocument();
    });

    it('displays placeholder text when query is empty', () => {
      renderDeckSearch({ query: '' });
      expect(screen.getByText('Search your decks')).toBeInTheDocument();
    });

    it('displays the search input', () => {
      const { container } = renderDeckSearch();
      expect(container.querySelector('.deck-search__form__input')).toBeInTheDocument();
    });

    it('displays formatted input container', () => {
      const { container } = renderDeckSearch();
      expect(container.querySelector('.deck-search__form__formatted-input')).toBeInTheDocument();
    });

    it('initializes with query from props', () => {
      const { container } = renderDeckSearch({ query: 'test query' });
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      expect(input.value).toBe('test query');
    });
  });

  describe('query input handling', () => {
    it('updates query when input changes', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.change(input, { target: { value: 'new query' } });
      
      expect(input.value).toBe('new query');
    });

    it('prevents default on input change', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      const changeEvent = new Event('change', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(changeEvent, 'preventDefault');
      
      fireEvent(input, changeEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('updates state when props change', () => {
      const { rerenderWithDispatcher } = renderDeckSearch({ query: 'initial' });
      
      rerenderWithDispatcher({ query: 'updated' });
      
      const { container } = renderDeckSearch({ query: 'updated' });
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      expect(input.value).toBe('updated');
    });
  });

  describe('form submission', () => {
    it('calls DeckActions.query when form is submitted', () => {
      const { dispatcher } = renderDeckSearch({ query: 'test query' });
      const { container } = renderDeckSearch({ query: 'test query' });
      const form = container.querySelector('.deck-search__form') as HTMLFormElement;
      
      fireEvent.submit(form);
      
      expect(mockQuery).toHaveBeenCalledWith(
        'test query',
        expect.any(String),
        expect.any(Object), // ColorFilter
        expect.any(Object), // tags Map
        expect.any(Object), // notTags Map
        dispatcher
      );
    });

    it('prevents default on form submission', () => {
      const { container } = renderDeckSearch();
      const form = container.querySelector('.deck-search__form') as HTMLFormElement;
      
      const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(submitEvent, 'preventDefault');
      const stopPropagationSpy = vi.spyOn(submitEvent, 'stopPropagation');
      
      fireEvent(form, submitEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(stopPropagationSpy).toHaveBeenCalled();
    });

    it('extracts color filters from query', () => {
      const { dispatcher } = renderDeckSearch({ query: 'c:white c:blue' });
      const { container } = renderDeckSearch({ query: 'c:white c:blue' });
      const form = container.querySelector('.deck-search__form') as HTMLFormElement;
      
      fireEvent.submit(form);
      
      expect(mockQuery).toHaveBeenCalledWith(
        'c:white c:blue',
        expect.any(String),
        expect.objectContaining({
          white: expect.any(String),
          blue: expect.any(String),
        }),
        expect.any(Object),
        expect.any(Object),
        dispatcher
      );
    });
  });

  describe('focus and blur handling', () => {
    it('adds focus class when input is focused', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      
      const form = container.querySelector('.deck-search__form');
      expect(form).toHaveClass('is-focus');
    });

    it('adds document click listener on focus', () => {
      const addEventListenerSpy = vi.spyOn(document, 'addEventListener');
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      
      expect(addEventListenerSpy).toHaveBeenCalledWith('click', expect.any(Function));
    });

    it('removes focus class on document click', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      
      // Simulate document click
      fireEvent.click(document);
      
      const form = container.querySelector('.deck-search__form');
      expect(form).not.toHaveClass('is-focus');
    });
  });

  describe('color suggestions', () => {
    it('shows color suggestions when typing color filter', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: 'c:wh' } });
      
      // Should show suggestions for colors matching 'wh'
      expect(screen.getByText('white')).toBeInTheDocument();
    });

    it('filters color suggestions based on input', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: 'c:bl' } });
      
      // Should show suggestions for colors matching 'bl'
      expect(screen.getByText('blue')).toBeInTheDocument();
      expect(screen.getByText('black')).toBeInTheDocument();
    });

    it('hides suggestions when not focused', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.change(input, { target: { value: 'c:wh' } });
      
      // Should not show suggestions when not focused
      expect(screen.queryByText('white')).not.toBeInTheDocument();
    });
  });

  describe('keyboard navigation', () => {
    it('navigates suggestions with arrow keys', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: 'c:' } });
      
      // Navigate down
      fireEvent.keyDown(input, { keyCode: 40 }); // DOWN
      
      // Should select first suggestion
      const selectedSuggestion = container.querySelector('.advanced-search-input__suggestion.is-selected');
      expect(selectedSuggestion).toBeInTheDocument();
    });

    it('prevents default on arrow key navigation', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: 'c:' } });
      
      const keyDownEvent = new KeyboardEvent('keydown', { keyCode: 40, bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(keyDownEvent, 'preventDefault');
      const stopPropagationSpy = vi.spyOn(keyDownEvent, 'stopPropagation');
      
      fireEvent(input, keyDownEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(stopPropagationSpy).toHaveBeenCalled();
    });

    it('accepts suggestion with Enter key', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: 'c:wh' } });
      fireEvent.keyDown(input, { keyCode: 40 }); // DOWN to select
      fireEvent.keyDown(input, { keyCode: 13 }); // ENTER to accept
      
      // Should replace the query with the selected suggestion
      expect(input.value).toContain('white');
    });
  });

  describe('suggestion clicking', () => {
    it('replaces query when suggestion is clicked', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: 'c:wh' } });
      
      const whiteSuggestion = screen.getByText('white');
      fireEvent.click(whiteSuggestion);
      
      expect(input.value).toContain('white');
    });

    it('prevents default when suggestion is clicked', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: 'c:wh' } });
      
      const whiteSuggestion = screen.getByText('white');
      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(clickEvent, 'preventDefault');
      const stopPropagationSpy = vi.spyOn(clickEvent, 'stopPropagation');
      
      fireEvent(whiteSuggestion, clickEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(stopPropagationSpy).toHaveBeenCalled();
    });

    it('focuses input after suggestion click', () => {
      const { container } = renderDeckSearch();
      const input = container.querySelector('.deck-search__form__input') as HTMLInputElement;
      
      fireEvent.focus(input);
      fireEvent.change(input, { target: { value: 'c:wh' } });
      
      const whiteSuggestion = screen.getByText('white');
      fireEvent.click(whiteSuggestion);
      
      expect(document.activeElement).toBe(input);
    });
  });

  describe('form click handling', () => {
    it('prevents default when form is clicked', () => {
      const { container } = renderDeckSearch();
      const form = container.querySelector('.deck-search__form') as HTMLFormElement;
      
      const clickEvent = new Event('click', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(clickEvent, 'preventDefault');
      
      fireEvent(form, clickEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });

  describe('query formatting', () => {
    it('formats color filters in the display', () => {
      const { container } = renderDeckSearch({ query: 'c:white test' });
      
      // Should format the color filter part
      const formattedInput = container.querySelector('.deck-search__form__formatted-input');
      expect(formattedInput).toBeInTheDocument();
    });

    it('preserves whitespace in formatted display', () => {
      const { container } = renderDeckSearch({ query: 'test  query' });
      
      const formattedSpan = container.querySelector('.deck-search__form__formatted-input span');
      expect(formattedSpan).toHaveStyle({ whiteSpace: 'pre-wrap' });
    });
  });
});
