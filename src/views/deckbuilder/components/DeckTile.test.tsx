import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { DeckTile } from './DeckTile';

// Mock history helper
vi.mock('../../../helpers/history', () => ({
  default: {
    push: vi.fn(),
  },
}));

// Mock CoreAssets helper
vi.mock('../../../helpers/core_assets', () => ({
  CoreAssets: {
    imageHost: vi.fn().mockReturnValue('https://example.com'),
  },
}));

import { CoreAssets } from '../../../helpers/core_assets';
import history from '../../../helpers/history';

const mockHistoryPush = vi.mocked(history.push);
const mockImageHost = vi.mocked(CoreAssets.imageHost);

type DeckTileProps = React.ComponentProps<typeof DeckTile>;

const defaultProps: Omit<DeckTileProps, 'dispatcher'> = {
  deck: create(FakeDeck, {
    name: 'Test Deck',
    uuid: 'test-deck-uuid',
    image: 'test-image',
    colorWhite: true,
    colorBlue: false,
    colorBlack: true,
    colorRed: false,
    colorGreen: true,
  }),
};

const renderDeckTile = (props: Partial<DeckTileProps> = {}) => {
  return renderWithDispatcher(DeckTile, { ...defaultProps, ...props });
};

describe('DeckTile', () => {
  beforeEach(() => {
    mockHistoryPush.mockClear();
    mockImageHost.mockReturnValue('https://example.com');
  });

  describe('when component renders', () => {
    it('displays the deck tile container', () => {
      const { container } = renderDeckTile();
      expect(container.querySelector('.deck-tile')).toBeInTheDocument();
    });

    it('displays the deck name', () => {
      renderDeckTile();
      expect(screen.getByText('Test Deck')).toBeInTheDocument();
    });

    it('displays the deck image wrapper', () => {
      const { container } = renderDeckTile();
      expect(container.querySelector('.deck-tile__image-wrapper')).toBeInTheDocument();
    });

    it('displays the color indicators', () => {
      const { container } = renderDeckTile();
      expect(container.querySelector('.deck-tile__colors')).toBeInTheDocument();
    });

    it('shows correct color indicators based on deck colors', () => {
      const { container } = renderDeckTile();

      // White should be on
      expect(container.querySelector('.deck-tile__colors__color.is-white.on')).toBeInTheDocument();

      // Blue should be off
      expect(container.querySelector('.deck-tile__colors__color.is-blue:not(.on)')).toBeInTheDocument();

      // Black should be on
      expect(container.querySelector('.deck-tile__colors__color.is-black.on')).toBeInTheDocument();

      // Red should be off
      expect(container.querySelector('.deck-tile__colors__color.is-red:not(.on)')).toBeInTheDocument();

      // Green should be on
      expect(container.querySelector('.deck-tile__colors__color.is-green.on')).toBeInTheDocument();
    });
  });

  describe('deck image handling', () => {
    it('uses deck image when available', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, { image: 'custom-image' }),
      });

      const imageElement = container.querySelector('.deck-tile__image-wrapper__image');
      expect(imageElement).toHaveStyle({
        backgroundImage: 'url(https://example.com/card_art_hq/custom-image.jpg)',
      });
    });

    it('uses default image when deck image is null', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, { image: null }),
      });

      const imageElement = container.querySelector('.deck-tile__image-wrapper__image');
      expect(imageElement).toHaveStyle({
        backgroundImage: 'url(/images/deck-avatar-default.jpg)',
      });
    });

    it('uses default image when deck image is "null" string', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, { image: 'null' }),
      });

      const imageElement = container.querySelector('.deck-tile__image-wrapper__image');
      expect(imageElement).toHaveStyle({
        backgroundImage: 'url(/images/deck-avatar-default.jpg)',
      });
    });

    it('uses default image when deck image is undefined', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, { image: undefined }),
      });

      const imageElement = container.querySelector('.deck-tile__image-wrapper__image');
      expect(imageElement).toHaveStyle({
        backgroundImage: 'url(/images/deck-avatar-default.jpg)',
      });
    });
  });

  describe('when user clicks on deck tile', () => {
    it('navigates to deck page', () => {
      renderDeckTile();
      const deckTile = screen.getByText('Test Deck').closest('.deck-tile');

      if (deckTile) {
        fireEvent.click(deckTile);
        expect(mockHistoryPush).toHaveBeenCalledWith('/decks/test-deck-uuid');
      }
    });

    it('prevents default event behavior', () => {
      renderDeckTile();
      const deckTile = screen.getByText('Test Deck').closest('.deck-tile');

      if (deckTile) {
        const clickEvent = new Event('click', { bubbles: true, cancelable: true });
        const preventDefaultSpy = vi.spyOn(clickEvent, 'preventDefault');

        fireEvent(deckTile, clickEvent);

        expect(preventDefaultSpy).toHaveBeenCalled();
      }
    });
  });

  describe('with different deck configurations', () => {
    it('renders deck with all colors enabled', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, {
          colorWhite: true,
          colorBlue: true,
          colorBlack: true,
          colorRed: true,
          colorGreen: true,
        }),
      });

      expect(container.querySelector('.deck-tile__colors__color.is-white.on')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-blue.on')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-black.on')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-red.on')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-green.on')).toBeInTheDocument();
    });

    it('renders deck with no colors enabled', () => {
      const { container } = renderDeckTile({
        deck: create(FakeDeck, {
          colorWhite: false,
          colorBlue: false,
          colorBlack: false,
          colorRed: false,
          colorGreen: false,
        }),
      });

      expect(container.querySelector('.deck-tile__colors__color.is-white:not(.on)')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-blue:not(.on)')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-black:not(.on)')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-red:not(.on)')).toBeInTheDocument();
      expect(container.querySelector('.deck-tile__colors__color.is-green:not(.on)')).toBeInTheDocument();
    });

    it('renders deck with long name', () => {
      const longName = 'This is a very long deck name that might wrap to multiple lines';
      renderDeckTile({
        deck: create(FakeDeck, { name: longName }),
      });

      expect(screen.getByText(longName)).toBeInTheDocument();
    });

    it('renders deck with special characters in name', () => {
      const specialName = 'Deck with "Special" & <Characters>';
      renderDeckTile({
        deck: create(FakeDeck, { name: specialName }),
      });

      expect(screen.getByText(specialName)).toBeInTheDocument();
    });
  });

  describe('component lifecycle', () => {
    it('initializes with empty state', () => {
      // This test ensures the component initializes properly
      const { container } = renderDeckTile();
      expect(container.querySelector('.deck-tile')).toBeInTheDocument();
    });

    it('has empty componentDidMount implementation', () => {
      // This test ensures the component mounts without errors
      const { container } = renderDeckTile();
      expect(container.querySelector('.deck-tile')).toBeInTheDocument();
    });
  });
});
