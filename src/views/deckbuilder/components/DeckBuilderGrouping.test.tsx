import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { Grouping } from '../../../models/Grouping';
import { DeckBuilderGrouping } from './DeckBuilderGrouping';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockGrouping = vi.mocked(DeckActions.grouping);

type DeckBuilderGroupingProps = React.ComponentProps<typeof DeckBuilderGrouping>;

const defaultProps: Omit<DeckBuilderGroupingProps, 'dispatcher'> = {
  deckGrouping: Grouping.NONE,
};

const renderDeckBuilderGrouping = (props: Partial<DeckBuilderGroupingProps> = {}) => {
  return renderWithDispatcher(DeckBuilderGrouping, { ...defaultProps, ...props });
};

describe('DeckBuilderGrouping', () => {
  describe('when component renders', () => {
    it('displays the group by heading', () => {
      renderDeckBuilderGrouping();
      expect(screen.getByText('Group By')).toBeInTheDocument();
    });

    it('displays the select dropdown with current grouping value', () => {
      renderDeckBuilderGrouping({ deckGrouping: Grouping.CARD_TYPE });
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(Grouping.CARD_TYPE);
    });

    it('displays all grouping options', () => {
      renderDeckBuilderGrouping();
      const select = screen.getByRole('combobox');
      
      // Check that all grouping options are present
      Object.values(Grouping).forEach((grouping) => {
        const option = screen.getByRole('option', { name: grouping.charAt(0).toUpperCase() + grouping.slice(1) });
        expect(option).toBeInTheDocument();
      });
    });

    it('displays disabled "Group by" option', () => {
      renderDeckBuilderGrouping();
      const disabledOption = screen.getByRole('option', { name: 'Group by' });
      expect(disabledOption).toBeInTheDocument();
      expect(disabledOption).toBeDisabled();
    });
  });

  describe('when user interacts with grouping selector', () => {
    it('calls DeckActions.grouping when selection changes', () => {
      const { dispatcher } = renderDeckBuilderGrouping();
      const select = screen.getByRole('combobox');
      
      fireEvent.change(select, { target: { value: Grouping.CARD_TYPE } });
      
      expect(mockGrouping).toHaveBeenCalledWith(Grouping.CARD_TYPE, dispatcher);
    });

    it('prevents default event behavior when selection changes', () => {
      renderDeckBuilderGrouping();
      const select = screen.getByRole('combobox');
      
      const changeEvent = new Event('change', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(changeEvent, 'preventDefault');
      
      fireEvent(select, changeEvent);
      
      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('handles different grouping values correctly', () => {
      const { dispatcher } = renderDeckBuilderGrouping();
      const select = screen.getByRole('combobox');
      
      // Test changing to different grouping values
      fireEvent.change(select, { target: { value: Grouping.MANA_COST } });
      expect(mockGrouping).toHaveBeenCalledWith(Grouping.MANA_COST, dispatcher);
      
      fireEvent.change(select, { target: { value: Grouping.RARITY } });
      expect(mockGrouping).toHaveBeenCalledWith(Grouping.RARITY, dispatcher);
    });
  });

  describe('when component has different initial grouping values', () => {
    it('renders with NONE grouping selected', () => {
      renderDeckBuilderGrouping({ deckGrouping: Grouping.NONE });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Grouping.NONE);
    });

    it('renders with CARD_TYPE grouping selected', () => {
      renderDeckBuilderGrouping({ deckGrouping: Grouping.CARD_TYPE });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Grouping.CARD_TYPE);
    });

    it('renders with MANA_COST grouping selected', () => {
      renderDeckBuilderGrouping({ deckGrouping: Grouping.MANA_COST });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Grouping.MANA_COST);
    });

    it('renders with RARITY grouping selected', () => {
      renderDeckBuilderGrouping({ deckGrouping: Grouping.RARITY });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Grouping.RARITY);
    });
  });
});
