import { fireEvent, screen } from '@testing-library/react';
import * as React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DeckActions from '../../../actions/DeckActions';
import { DeckSorting as Sorting } from '../../../models/sorting/DeckSorting';
import { DeckSorting } from './DeckSorting';

// Mock DeckActions
vi.mock('../../../actions/DeckActions');
const mockSorting = vi.mocked(DeckActions.sorting);

type DeckSortingProps = React.ComponentProps<typeof DeckSorting>;

const defaultProps: Omit<DeckSortingProps, 'dispatcher'> = {
  sorting: Sorting.NAME_ASC,
};

const renderDeckSorting = (props: Partial<DeckSortingProps> = {}) => {
  return renderWithDispatcher(DeckSorting, { ...defaultProps, ...props });
};

describe('DeckSorting', () => {
  beforeEach(() => {
    mockSorting.mockClear();
  });

  describe('when component renders', () => {
    it('displays the icon select container', () => {
      const { container } = renderDeckSorting();
      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
    });

    it('displays the select dropdown with current sorting value', () => {
      renderDeckSorting({ sorting: Sorting.DATE_ADDED_DESC });
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
      expect(select).toHaveValue(Sorting.DATE_ADDED_DESC);
    });

    it('displays disabled "Sort By" option', () => {
      renderDeckSorting();
      const disabledOption = screen.getByRole('option', { name: 'Sort By' });
      expect(disabledOption).toBeInTheDocument();
      expect(disabledOption).toBeDisabled();
    });

    it('displays all sorting options', () => {
      renderDeckSorting();

      // Check that all sorting options are present
      Object.keys(Sorting).forEach((key) => {
        const sortingValue = Sorting[key as keyof typeof Sorting];
        const option = screen.getByRole('option', { name: sortingValue });
        expect(option).toBeInTheDocument();
      });
    });

    it('applies correct CSS classes', () => {
      const { container } = renderDeckSorting();

      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
      expect(container.querySelector('.deck-select__option')).toBeInTheDocument();

      const sortingOptions = container.querySelectorAll('.deck-sorting__select__option');
      expect(sortingOptions.length).toBeGreaterThan(0);
    });
  });

  describe('sorting options', () => {
    it('includes name ascending option', () => {
      renderDeckSorting();
      expect(screen.getByRole('option', { name: Sorting.NAME_ASC })).toBeInTheDocument();
    });

    it('includes name descending option', () => {
      renderDeckSorting();
      expect(screen.getByRole('option', { name: Sorting.NAME_DESC })).toBeInTheDocument();
    });

    it('includes date added ascending option', () => {
      renderDeckSorting();
      expect(screen.getByRole('option', { name: Sorting.DATE_ADDED_ASC })).toBeInTheDocument();
    });

    it('includes date added descending option', () => {
      renderDeckSorting();
      expect(screen.getByRole('option', { name: Sorting.DATE_ADDED_DESC })).toBeInTheDocument();
    });

    it('includes date edited ascending option', () => {
      renderDeckSorting();
      expect(screen.getByRole('option', { name: Sorting.DATE_EDITED_ASC })).toBeInTheDocument();
    });

    it('includes date edited descending option', () => {
      renderDeckSorting();
      expect(screen.getByRole('option', { name: Sorting.DATE_EDITED_DESC })).toBeInTheDocument();
    });

    it('includes price ascending option', () => {
      renderDeckSorting();
      expect(screen.getByRole('option', { name: Sorting.PRICE_ASC })).toBeInTheDocument();
    });

    it('includes price descending option', () => {
      renderDeckSorting();
      expect(screen.getByRole('option', { name: Sorting.PRICE_DESC })).toBeInTheDocument();
    });
  });

  describe('when user interacts with sorting selector', () => {
    it('calls DeckActions.sorting when selection changes', () => {
      const { dispatcher } = renderDeckSorting();
      const select = screen.getByRole('combobox');

      fireEvent.change(select, { target: { value: Sorting.DATE_ADDED_DESC } });

      expect(mockSorting).toHaveBeenCalledWith(Sorting.DATE_ADDED_DESC, dispatcher);
    });

    it('prevents default event behavior when selection changes', () => {
      renderDeckSorting();
      const select = screen.getByRole('combobox');

      const changeEvent = new Event('change', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(changeEvent, 'preventDefault');

      fireEvent(select, changeEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('handles different sorting values correctly', () => {
      const { dispatcher } = renderDeckSorting();
      const select = screen.getByRole('combobox');

      // Test changing to different sorting values
      fireEvent.change(select, { target: { value: Sorting.NAME_DESC } });
      expect(mockSorting).toHaveBeenCalledWith(Sorting.NAME_DESC, dispatcher);

      fireEvent.change(select, { target: { value: Sorting.PRICE_ASC } });
      expect(mockSorting).toHaveBeenCalledWith(Sorting.PRICE_ASC, dispatcher);

      fireEvent.change(select, { target: { value: Sorting.DATE_EDITED_DESC } });
      expect(mockSorting).toHaveBeenCalledWith(Sorting.DATE_EDITED_DESC, dispatcher);
    });
  });

  describe('when component has different initial sorting values', () => {
    it('renders with NAME_ASC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.NAME_ASC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.NAME_ASC);
    });

    it('renders with NAME_DESC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.NAME_DESC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.NAME_DESC);
    });

    it('renders with DATE_ADDED_ASC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.DATE_ADDED_ASC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.DATE_ADDED_ASC);
    });

    it('renders with DATE_ADDED_DESC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.DATE_ADDED_DESC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.DATE_ADDED_DESC);
    });

    it('renders with DATE_EDITED_ASC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.DATE_EDITED_ASC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.DATE_EDITED_ASC);
    });

    it('renders with DATE_EDITED_DESC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.DATE_EDITED_DESC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.DATE_EDITED_DESC);
    });

    it('renders with PRICE_ASC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.PRICE_ASC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.PRICE_ASC);
    });

    it('renders with PRICE_DESC sorting selected', () => {
      renderDeckSorting({ sorting: Sorting.PRICE_DESC });
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.PRICE_DESC);
    });
  });

  describe('component lifecycle', () => {
    it('initializes with empty state', () => {
      renderDeckSorting();

      // Component should initialize without errors
      const { container } = renderDeckSorting();
      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
    });

    it('has empty componentDidMount implementation', () => {
      // This test ensures the component mounts without errors
      const { container } = renderDeckSorting();
      expect(container.querySelector('.icon-container--deck')).toBeInTheDocument();
    });
  });

  describe('IconSelect integration', () => {
    it('passes correct props to IconSelect', () => {
      const { container } = renderDeckSorting({ sorting: Sorting.NAME_DESC });

      // IconSelect should receive the correct className, icon, value, and onChange
      const iconSelect = container.querySelector('.icon-container--deck');
      expect(iconSelect).toBeInTheDocument();

      // The select should have the correct value
      const select = screen.getByRole('combobox');
      expect(select).toHaveValue(Sorting.NAME_DESC);
    });

    it('renders sort icon', () => {
      const { container } = renderDeckSorting();

      // Should render the sort icon (this would be tested more thoroughly in IconSelect tests)
      const iconContainer = container.querySelector('.icon-container--deck');
      expect(iconContainer).toBeInTheDocument();
    });
  });

  describe('option rendering', () => {
    it('generates unique keys for options', () => {
      renderDeckSorting();

      // Each option should have a unique key (tested indirectly by ensuring no React warnings)
      const options = screen.getAllByRole('option');
      expect(options.length).toBeGreaterThan(1); // At least the disabled option plus sorting options
    });

    it('uses correct option values', () => {
      renderDeckSorting();

      Object.keys(Sorting).forEach((key) => {
        const sortingValue = Sorting[key as keyof typeof Sorting];
        const option = screen.getByRole('option', { name: sortingValue });
        expect(option).toHaveValue(sortingValue);
      });
    });

    it('applies correct CSS classes to options', () => {
      const { container } = renderDeckSorting();

      const disabledOption = container.querySelector('.deck-select__option');
      expect(disabledOption).toBeInTheDocument();

      const sortingOptions = container.querySelectorAll('.deck-sorting__select__option');
      expect(sortingOptions.length).toBe(Object.keys(Sorting).length);
    });
  });

  describe('accessibility', () => {
    it('provides proper select element', () => {
      renderDeckSorting();
      const select = screen.getByRole('combobox');
      expect(select).toBeInTheDocument();
    });

    it('provides proper option elements', () => {
      renderDeckSorting();
      const options = screen.getAllByRole('option');
      expect(options.length).toBe(Object.keys(Sorting).length + 1); // +1 for disabled option
    });

    it('marks disabled option correctly', () => {
      renderDeckSorting();
      const disabledOption = screen.getByRole('option', { name: 'Sort By' });
      expect(disabledOption).toBeDisabled();
    });
  });
});
