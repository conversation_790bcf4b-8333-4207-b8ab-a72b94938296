import { fireEvent, screen } from '@testing-library/react';
import { render } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { DeckShareLink } from './DeckShareLink';

// Mock navigator.clipboard
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(),
  },
});

const mockWriteText = vi.mocked(navigator.clipboard.writeText);

type DeckShareLinkProps = React.ComponentProps<typeof DeckShareLink>;

const defaultProps: DeckShareLinkProps = {
  href: 'https://example.com/deck/123',
  urlText: 'https://example.com/deck/123',
};

const renderDeckShareLink = (props: Partial<DeckShareLinkProps> = {}) => {
  return render(<DeckShareLink {...defaultProps} {...props} />);
};

describe('DeckShareLink', () => {
  beforeEach(() => {
    mockWriteText.mockClear();
    mockWriteText.mockResolvedValue(undefined);
  });

  describe('when component renders', () => {
    it('displays the share link', () => {
      renderDeckShareLink();
      const link = screen.getByRole('link');
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', 'https://example.com/deck/123');
      expect(link).toHaveAttribute('target', '_blank');
    });

    it('displays the URL text in the link', () => {
      renderDeckShareLink();
      expect(screen.getByText('https://example.com/deck/123')).toBeInTheDocument();
    });

    it('displays the copy button with initial text', () => {
      renderDeckShareLink();
      expect(screen.getByRole('button', { name: 'Copy URL' })).toBeInTheDocument();
    });

    it('displays the external link icon', () => {
      const { container } = renderDeckShareLink();
      const icon = container.querySelector('svg');
      expect(icon).toBeInTheDocument();
    });

    it('uses correct CSS classes for layout', () => {
      const { container } = renderDeckShareLink();
      
      expect(container.querySelector('.col-xs-12.col-md-9')).toBeInTheDocument();
      expect(container.querySelector('.col-xs-12.col-md-3')).toBeInTheDocument();
      expect(container.querySelector('.deckbuilder-link-container')).toBeInTheDocument();
    });
  });

  describe('when copy button is clicked', () => {
    it('copies URL text to clipboard', async () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      
      fireEvent.click(copyButton);
      
      expect(mockWriteText).toHaveBeenCalledWith('https://example.com/deck/123');
    });

    it('changes button text to "Copied"', () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      
      fireEvent.click(copyButton);
      
      expect(screen.getByRole('button', { name: 'Copied' })).toBeInTheDocument();
    });

    it('blurs the button after clicking', () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      const blurSpy = vi.spyOn(copyButton, 'blur');
      
      fireEvent.click(copyButton);
      
      expect(blurSpy).toHaveBeenCalled();
    });
  });

  describe('with different props', () => {
    it('renders with custom href', () => {
      renderDeckShareLink({ href: 'https://custom.com/deck/456' });
      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', 'https://custom.com/deck/456');
    });

    it('renders with custom URL text', () => {
      renderDeckShareLink({ urlText: 'Custom Deck URL' });
      expect(screen.getByText('Custom Deck URL')).toBeInTheDocument();
    });

    it('copies custom URL text to clipboard', () => {
      renderDeckShareLink({ urlText: 'Custom Deck URL' });
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      
      fireEvent.click(copyButton);
      
      expect(mockWriteText).toHaveBeenCalledWith('Custom Deck URL');
    });

    it('handles long URLs', () => {
      const longUrl = 'https://example.com/very/long/path/to/deck/with/many/parameters?id=123&format=standard&colors=red,blue';
      renderDeckShareLink({ href: longUrl, urlText: longUrl });
      
      expect(screen.getByText(longUrl)).toBeInTheDocument();
      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', longUrl);
    });

    it('handles URLs with special characters', () => {
      const specialUrl = 'https://example.com/deck/test%20deck?name=My%20Deck&format=standard';
      renderDeckShareLink({ href: specialUrl, urlText: specialUrl });
      
      expect(screen.getByText(specialUrl)).toBeInTheDocument();
      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', specialUrl);
    });
  });

  describe('component state', () => {
    it('initializes with "Copy URL" text', () => {
      renderDeckShareLink();
      expect(screen.getByRole('button', { name: 'Copy URL' })).toBeInTheDocument();
    });

    it('maintains "Copied" state after clicking', () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      
      fireEvent.click(copyButton);
      
      expect(screen.getByRole('button', { name: 'Copied' })).toBeInTheDocument();
      expect(screen.queryByRole('button', { name: 'Copy URL' })).not.toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('has proper link attributes for external navigation', () => {
      renderDeckShareLink();
      const link = screen.getByRole('link');
      
      expect(link).toHaveAttribute('target', '_blank');
      // Note: In a real implementation, you might want to add rel="noopener noreferrer" for security
    });

    it('has identifiable button for copy action', () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      expect(copyButton).toHaveAttribute('id', 'share-button');
    });

    it('provides visual feedback when copy action is performed', () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      
      fireEvent.click(copyButton);
      
      // Button text changes to provide feedback
      expect(screen.getByRole('button', { name: 'Copied' })).toBeInTheDocument();
    });
  });

  describe('error handling', () => {
    it('handles clipboard write errors gracefully', () => {
      mockWriteText.mockRejectedValue(new Error('Clipboard not available'));
      
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      
      // Should not throw an error
      expect(() => fireEvent.click(copyButton)).not.toThrow();
      
      // Button text should still change
      expect(screen.getByRole('button', { name: 'Copied' })).toBeInTheDocument();
    });
  });

  describe('component styling', () => {
    it('applies correct button styling', () => {
      renderDeckShareLink();
      const copyButton = screen.getByRole('button', { name: 'Copy URL' });
      expect(copyButton).toHaveClass('button-primary');
    });

    it('uses responsive grid layout', () => {
      const { container } = renderDeckShareLink();
      
      // Link container uses responsive columns
      expect(container.querySelector('.col-xs-12.col-md-9')).toBeInTheDocument();
      
      // Button container uses responsive columns
      expect(container.querySelector('.col-xs-12.col-md-3')).toBeInTheDocument();
    });
  });
});
