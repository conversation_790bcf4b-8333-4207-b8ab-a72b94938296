import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import { DeckStatisticsColorStack } from './DeckStatisticsColorStack';

// Mock Plotly
const mockPlotly = {
  newPlot: vi.fn(),
  purge: vi.fn(),
};

vi.mock('../../../helpers/plotly', () => ({
  default: vi.fn(() => mockPlotly),
}));

// Mock serverside helper
vi.mock('../../../helpers/serverside', () => ({
  default: vi.fn(() => false),
}));

type DeckStatisticsColorStackProps = React.ComponentProps<typeof DeckStatisticsColorStack>;

const createMockStatistics = () => {
  return Immutable.Map({
    white: Immutable.Map({ 1: 5, 2: 3, 3: 2 }),
    blue: Immutable.Map({ 1: 4, 2: 6, 3: 1 }),
    black: Immutable.Map({ 1: 2, 2: 4, 3: 3 }),
    red: Immutable.Map({ 1: 6, 2: 2, 3: 1 }),
    green: Immutable.Map({ 1: 3, 2: 5, 3: 4 }),
    multi: Immutable.Map({ 1: 1, 2: 2, 3: 1 }),
    colorless: Immutable.Map({ 1: 2, 2: 1, 3: 0 }),
  });
};

const defaultProps: Omit<DeckStatisticsColorStackProps, 'dispatcher'> = {
  title: 'Mana Curve',
  statistics: createMockStatistics(),
  mini: false,
};

const renderDeckStatisticsColorStack = (props: Partial<DeckStatisticsColorStackProps> = {}) => {
  return renderWithDispatcher(DeckStatisticsColorStack, { ...defaultProps, ...props });
};

describe('DeckStatisticsColorStack', () => {
  beforeEach(() => {
    mockPlotly.newPlot.mockClear();
    mockPlotly.purge.mockClear();
  });

  describe('when component renders', () => {
    it('displays the chart container', () => {
      const { container } = renderDeckStatisticsColorStack();
      expect(container.querySelector('div[id="Mana Curve"]')).toBeInTheDocument();
    });

    it('applies correct styling for normal size', () => {
      const { container } = renderDeckStatisticsColorStack({ mini: false });
      const chartContainer = container.firstChild as HTMLElement;
      
      expect(chartContainer).toHaveStyle({
        position: 'relative',
        width: '100%',
        minWidth: '0',
        maxWidth: '100%',
        height: '30rem',
        maxHeight: '30rem',
      });
    });

    it('applies correct styling for mini size', () => {
      const { container } = renderDeckStatisticsColorStack({ mini: true });
      const chartContainer = container.firstChild as HTMLElement;
      
      expect(chartContainer).toHaveStyle({
        position: 'relative',
        width: '100%',
        minWidth: '350px',
        maxWidth: '350px',
        height: '30rem',
        maxHeight: '300px',
      });
    });

    it('creates chart element with correct id', () => {
      const { container } = renderDeckStatisticsColorStack({ title: 'Custom Chart' });
      expect(container.querySelector('div[id="Custom Chart"]')).toBeInTheDocument();
    });

    it('applies correct styling to chart element', () => {
      const { container } = renderDeckStatisticsColorStack();
      const chartElement = container.querySelector('div[id="Mana Curve"]');
      
      expect(chartElement).toHaveStyle({
        width: '100%',
        maxWidth: '100%',
        height: '100%',
        maxHeight: '100%',
      });
    });
  });

  describe('chart rendering', () => {
    it('calls Plotly.newPlot on mount', () => {
      renderDeckStatisticsColorStack();
      
      expect(mockPlotly.newPlot).toHaveBeenCalledWith(
        expect.any(HTMLElement),
        expect.any(Array),
        expect.any(Object),
        expect.any(Object)
      );
    });

    it('calls Plotly.newPlot on update', () => {
      const { rerenderWithDispatcher } = renderDeckStatisticsColorStack();
      
      mockPlotly.newPlot.mockClear();
      
      rerenderWithDispatcher({
        statistics: Immutable.Map({
          white: Immutable.Map({ 1: 10 }),
          blue: Immutable.Map({ 1: 5 }),
          black: Immutable.Map({ 1: 3 }),
          red: Immutable.Map({ 1: 2 }),
          green: Immutable.Map({ 1: 1 }),
          multi: Immutable.Map({ 1: 0 }),
          colorless: Immutable.Map({ 1: 0 }),
        }),
      });
      
      expect(mockPlotly.newPlot).toHaveBeenCalled();
    });

    it('creates data for all color categories', () => {
      renderDeckStatisticsColorStack();
      
      const [, data] = mockPlotly.newPlot.mock.calls[0];
      expect(data).toHaveLength(7); // white, blue, black, red, green, multi, colorless
    });

    it('applies correct bar chart configuration', () => {
      renderDeckStatisticsColorStack();
      
      const [, data] = mockPlotly.newPlot.mock.calls[0];
      data.forEach((series: any) => {
        expect(series.type).toBe('bar');
        expect(series).toHaveProperty('x');
        expect(series).toHaveProperty('y');
        expect(series).toHaveProperty('name');
        expect(series).toHaveProperty('marker');
      });
    });

    it('capitalizes color names correctly', () => {
      renderDeckStatisticsColorStack();
      
      const [, data] = mockPlotly.newPlot.mock.calls[0];
      const names = data.map((series: any) => series.name);
      
      expect(names).toContain('White');
      expect(names).toContain('Blue');
      expect(names).toContain('Black');
      expect(names).toContain('Red');
      expect(names).toContain('Green');
      expect(names).toContain('Multi');
      expect(names).toContain('Colorless');
    });

    it('applies correct layout configuration', () => {
      renderDeckStatisticsColorStack({ title: 'Test Chart' });
      
      const [, , layout] = mockPlotly.newPlot.mock.calls[0];
      expect(layout).toMatchObject({
        title: 'Test Chart',
        showlegend: false,
        barmode: 'stack',
        font: {
          family: 'lato-bold',
          size: 18,
          color: '#3e3e3e',
        },
      });
    });

    it('configures x-axis correctly for normal size', () => {
      renderDeckStatisticsColorStack({ mini: false });
      
      const [, , layout] = mockPlotly.newPlot.mock.calls[0];
      expect(layout.xaxis).toMatchObject({
        fixedrange: true,
        tick0: 0,
        dtick: 1,
      });
    });

    it('configures x-axis correctly for mini size', () => {
      renderDeckStatisticsColorStack({ mini: true });
      
      const [, , layout] = mockPlotly.newPlot.mock.calls[0];
      expect(layout.xaxis).toMatchObject({
        fixedrange: true,
        tick0: 0,
        dtick: 5,
      });
    });

    it('configures y-axis correctly for normal size', () => {
      renderDeckStatisticsColorStack({ mini: false });
      
      const [, , layout] = mockPlotly.newPlot.mock.calls[0];
      expect(layout.yaxis).toMatchObject({
        fixedrange: true,
        tickmode: 'auto',
        tick0: 0,
        dtick: 2,
        nticks: 16,
        rangemode: 'tozero',
        autorange: true,
      });
    });

    it('configures y-axis correctly for mini size', () => {
      renderDeckStatisticsColorStack({ mini: true });
      
      const [, , layout] = mockPlotly.newPlot.mock.calls[0];
      expect(layout.yaxis).toMatchObject({
        fixedrange: true,
        tickmode: 'auto',
        tick0: 0,
        dtick: 4,
        nticks: 8,
        rangemode: 'tozero',
        autorange: true,
      });
    });

    it('applies correct config options', () => {
      renderDeckStatisticsColorStack();
      
      const [, , , config] = mockPlotly.newPlot.mock.calls[0];
      expect(config).toMatchObject({
        staticPlot: false,
        editable: false,
        scrollZoom: false,
        showTips: false,
        showLink: false,
        sendData: false,
        showSources: false,
        displayModeBar: false,
        modeBarButtons: false,
        logging: false,
      });
    });
  });

  describe('color mapping', () => {
    it('applies correct colors for each mana color', () => {
      renderDeckStatisticsColorStack();
      
      const [, data] = mockPlotly.newPlot.mock.calls[0];
      const colorMap = data.reduce((map: any, series: any) => {
        map[series.name.toLowerCase()] = series.marker.color;
        return map;
      }, {});
      
      expect(colorMap.white).toBe('#FFE48B');
      expect(colorMap.blue).toBe('#45C3FF');
      expect(colorMap.black).toBe('#676767');
      expect(colorMap.red).toBe('#FF5C35');
      expect(colorMap.green).toBe('#1AC655');
      expect(colorMap.multi).toBe('#EDAE49');
      expect(colorMap.colorless).toBe('#C5C5C5');
    });
  });

  describe('data processing', () => {
    it('extracts correct x and y values from statistics', () => {
      const statistics = Immutable.Map({
        white: Immutable.Map({ 1: 5, 2: 3, 3: 2 }),
        blue: Immutable.Map({ 1: 4, 2: 6, 3: 1 }),
        black: Immutable.Map({ 1: 2, 2: 4, 3: 3 }),
        red: Immutable.Map({ 1: 6, 2: 2, 3: 1 }),
        green: Immutable.Map({ 1: 3, 2: 5, 3: 4 }),
        multi: Immutable.Map({ 1: 1, 2: 2, 3: 1 }),
        colorless: Immutable.Map({ 1: 2, 2: 1, 3: 0 }),
      });
      
      renderDeckStatisticsColorStack({ statistics });
      
      const [, data] = mockPlotly.newPlot.mock.calls[0];
      const whiteSeries = data.find((series: any) => series.name === 'White');
      
      expect(whiteSeries.x).toEqual([1, 2, 3]);
      expect(whiteSeries.y).toEqual([5, 3, 2]);
    });

    it('calculates correct x-axis range based on max count', () => {
      const statistics = Immutable.Map({
        white: Immutable.Map({ 1: 5, 2: 3, 10: 2 }), // max key is 10
        blue: Immutable.Map({ 1: 4, 2: 6, 3: 1 }),
        black: Immutable.Map({ 1: 2, 2: 4, 3: 3 }),
        red: Immutable.Map({ 1: 6, 2: 2, 3: 1 }),
        green: Immutable.Map({ 1: 3, 2: 5, 3: 4 }),
        multi: Immutable.Map({ 1: 1, 2: 2, 3: 1 }),
        colorless: Immutable.Map({ 1: 2, 2: 1, 3: 0 }),
      });
      
      renderDeckStatisticsColorStack({ statistics });
      
      const [, , layout] = mockPlotly.newPlot.mock.calls[0];
      expect(layout.xaxis.range).toEqual([-0.5, 10.5]);
    });
  });

  describe('window resize handling', () => {
    it('adds resize event listener on mount', () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener');
      
      renderDeckStatisticsColorStack();
      
      expect(addEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function));
    });

    it('removes resize event listener on unmount', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');
      
      const { unmount } = renderDeckStatisticsColorStack();
      unmount();
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('resize', expect.any(Function));
    });

    it('purges and re-renders chart on resize', () => {
      const { container } = renderDeckStatisticsColorStack({ title: 'Resize Test' });
      
      // Simulate window resize
      const resizeEvent = new Event('resize');
      window.dispatchEvent(resizeEvent);
      
      expect(mockPlotly.purge).toHaveBeenCalledWith('Resize Test');
      expect(mockPlotly.newPlot).toHaveBeenCalledTimes(2); // Once on mount, once on resize
    });
  });

  describe('edge cases', () => {
    it('handles empty statistics', () => {
      const emptyStatistics = Immutable.Map({
        white: Immutable.Map(),
        blue: Immutable.Map(),
        black: Immutable.Map(),
        red: Immutable.Map(),
        green: Immutable.Map(),
        multi: Immutable.Map(),
        colorless: Immutable.Map(),
      });
      
      renderDeckStatisticsColorStack({ statistics: emptyStatistics });
      
      const [, data] = mockPlotly.newPlot.mock.calls[0];
      data.forEach((series: any) => {
        expect(series.x).toEqual([]);
        expect(series.y).toEqual([]);
      });
    });

    it('handles statistics with zero values', () => {
      const zeroStatistics = Immutable.Map({
        white: Immutable.Map({ 1: 0, 2: 0 }),
        blue: Immutable.Map({ 1: 0, 2: 0 }),
        black: Immutable.Map({ 1: 0, 2: 0 }),
        red: Immutable.Map({ 1: 0, 2: 0 }),
        green: Immutable.Map({ 1: 0, 2: 0 }),
        multi: Immutable.Map({ 1: 0, 2: 0 }),
        colorless: Immutable.Map({ 1: 0, 2: 0 }),
      });
      
      renderDeckStatisticsColorStack({ statistics: zeroStatistics });
      
      const [, data] = mockPlotly.newPlot.mock.calls[0];
      const whiteSeries = data.find((series: any) => series.name === 'White');
      expect(whiteSeries.y).toEqual([0, 0]);
    });
  });

  describe('component lifecycle', () => {
    it('initializes with empty state', () => {
      renderDeckStatisticsColorStack();
      
      // Component should initialize without errors
      const { container } = renderDeckStatisticsColorStack();
      expect(container.querySelector('div[id="Mana Curve"]')).toBeInTheDocument();
    });

    it('cleans up properly on unmount', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');
      
      const { unmount } = renderDeckStatisticsColorStack();
      unmount();
      
      expect(removeEventListenerSpy).toHaveBeenCalled();
    });
  });

  describe('serverside rendering', () => {
    it('skips chart rendering on server side', () => {
      // Mock serverside to return true
      vi.mocked(require('../../../helpers/serverside').default).mockReturnValue(true);
      
      renderDeckStatisticsColorStack();
      
      expect(mockPlotly.newPlot).not.toHaveBeenCalled();
      
      // Reset mock
      vi.mocked(require('../../../helpers/serverside').default).mockReturnValue(false);
    });
  });
});
