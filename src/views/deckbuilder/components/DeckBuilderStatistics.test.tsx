import { screen } from '@testing-library/react';
import * as Immutable from 'immutable';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { renderWithDispatcher } from '../../../../tests/test-utils';
import * as DecksModule from '../../../models/Decks';
import { DeckBuilderStatistics } from './DeckBuilderStatistics';

// Mock the deck calculation functions
vi.mock('../../../models/Decks', async () => {
  const actual = await vi.importActual('../../../models/Decks');
  return {
    ...actual,
    calculateCardTypeRatios: vi.fn(),
    calculateManaSymbolRatios: vi.fn(),
    calculateManaSymbolStack: vi.fn(),
  };
});

const mockCalculateCardTypeRatios = vi.mocked(DecksModule.calculateCardTypeRatios);
const mockCalculateManaSymbolRatios = vi.mocked(DecksModule.calculateManaSymbolRatios);
const mockCalculateManaSymbolStack = vi.mocked(DecksModule.calculateManaSymbolStack);

type DeckBuilderStatisticsProps = React.ComponentProps<typeof DeckBuilderStatistics>;

const defaultProps: Omit<DeckBuilderStatisticsProps, 'dispatcher'> = {
  deck: create(FakeDeck),
};

const renderDeckBuilderStatistics = (props: Partial<DeckBuilderStatisticsProps> = {}) => {
  return renderWithDispatcher(DeckBuilderStatistics, { ...defaultProps, ...props });
};

describe('DeckBuilderStatistics', () => {
  beforeEach(() => {
    // Setup default mock return values
    mockCalculateCardTypeRatios.mockReturnValue(
      Immutable.Map({
        Creature: 0.4,
        Instant: 0.3,
        Sorcery: 0.2,
        Land: 0.1,
      }),
    );

    mockCalculateManaSymbolRatios.mockReturnValue(
      Immutable.Map({
        totalCount: 60,
        red: 0.3,
        blue: 0.2,
        white: 0.2,
        black: 0.2,
        green: 0.1,
        disabled: 0,
      }),
    );

    mockCalculateManaSymbolStack.mockReturnValue(
      Immutable.Map({
        1: Immutable.Map({ white: 5, blue: 3 }),
        2: Immutable.Map({ red: 8, green: 2 }),
        3: Immutable.Map({ black: 6 }),
      }),
    );
  });

  describe('when component renders', () => {
    it('renders the deck statistics container', () => {
      const { container } = renderDeckBuilderStatistics();
      expect(container.querySelector('.deck-statistics')).toBeInTheDocument();
    });

    it('calls calculation functions with the provided deck', () => {
      const deck = create(FakeDeck);
      renderDeckBuilderStatistics({ deck });

      expect(mockCalculateCardTypeRatios).toHaveBeenCalledWith(deck);
      expect(mockCalculateManaSymbolRatios).toHaveBeenCalledWith(deck);
      expect(mockCalculateManaSymbolStack).toHaveBeenCalledWith(deck);
    });

    it('renders three statistics sections', () => {
      const { container } = renderDeckBuilderStatistics();
      const statisticsColumns = container.querySelectorAll('.col-xs-12');
      expect(statisticsColumns).toHaveLength(3);
    });

    it('renders card types statistics with correct title', () => {
      renderDeckBuilderStatistics();
      expect(screen.getByText('Card Types')).toBeInTheDocument();
    });

    it('renders mana symbols statistics with correct title', () => {
      renderDeckBuilderStatistics();
      expect(screen.getByText('Mana Symbols')).toBeInTheDocument();
    });

    it('renders mana curve statistics with correct title', () => {
      renderDeckBuilderStatistics();
      expect(screen.getByText('Mana Curve')).toBeInTheDocument();
    });
  });

  describe('when deck data changes', () => {
    it('recalculates statistics when deck prop changes', () => {
      const initialDeck = create(FakeDeck, { name: 'Initial Deck' });
      const { rerenderWithDispatcher } = renderDeckBuilderStatistics({ deck: initialDeck });

      expect(mockCalculateCardTypeRatios).toHaveBeenCalledWith(initialDeck);

      const newDeck = create(FakeDeck, { name: 'New Deck' });
      rerenderWithDispatcher({ deck: newDeck });

      expect(mockCalculateCardTypeRatios).toHaveBeenCalledWith(newDeck);
    });

    it('filters out totalCount and disabled from mana symbol ratios', () => {
      renderDeckBuilderStatistics();
      
      // Verify that the mana symbol ratios are processed correctly
      expect(mockCalculateManaSymbolRatios).toHaveBeenCalled();
      
      // The component should call remove('totalCount').remove('disabled') on the result
      // This is tested indirectly by ensuring the component renders without errors
      expect(screen.getByText('Mana Symbols')).toBeInTheDocument();
    });
  });

  describe('when calculation functions return empty data', () => {
    it('handles empty card type ratios', () => {
      mockCalculateCardTypeRatios.mockReturnValue(Immutable.Map());
      
      renderDeckBuilderStatistics();
      expect(screen.getByText('Card Types')).toBeInTheDocument();
    });

    it('handles empty mana symbol ratios', () => {
      mockCalculateManaSymbolRatios.mockReturnValue(Immutable.Map());
      
      renderDeckBuilderStatistics();
      expect(screen.getByText('Mana Symbols')).toBeInTheDocument();
    });

    it('handles empty mana symbol stack', () => {
      mockCalculateManaSymbolStack.mockReturnValue(Immutable.Map());
      
      renderDeckBuilderStatistics();
      expect(screen.getByText('Mana Curve')).toBeInTheDocument();
    });
  });

  describe('component lifecycle', () => {
    it('has empty componentDidMount implementation', () => {
      // This test ensures the component mounts without errors
      const { container } = renderDeckBuilderStatistics();
      expect(container.querySelector('.deck-statistics')).toBeInTheDocument();
    });

    it('initializes with empty state', () => {
      // This test ensures the component initializes properly
      const { container } = renderDeckBuilderStatistics();
      expect(container.querySelector('.deck-statistics')).toBeInTheDocument();
    });
  });
});
