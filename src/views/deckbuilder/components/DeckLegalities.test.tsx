import { screen } from '@testing-library/react';
import { render } from '@testing-library/react';
import * as React from 'react';
import { describe, expect, it, vi } from 'vitest';
import { create } from '../../../../tests/fake/Fake';
import { FakeDeck } from '../../../../tests/fake/FakeCardData';
import { Legality } from '../../../models/Legality';
import { DeckLegalities } from './DeckLegalities';

// Mock the Validator
vi.mock('../../../lib/deckbuilder/validators/DeckValidator', () => ({
  Validator: vi.fn(),
}));

// Mock TextFormat
vi.mock('../../../helpers/fmt', () => ({
  TextFormat: {
    capitalize: vi.fn((str: string) => str.charAt(0).toUpperCase() + str.slice(1)),
  },
}));

import { Validator } from '../../../lib/deckbuilder/validators/DeckValidator';
const mockValidator = vi.mocked(Validator);

type DeckLegalitiesProps = React.ComponentProps<typeof DeckLegalities>;

const defaultProps: DeckLegalitiesProps = {
  deck: create(FakeDeck),
  isBuilding: false,
};

const renderDeckLegalities = (props: Partial<DeckLegalitiesProps> = {}) => {
  return render(<DeckLegalities {...defaultProps} {...props} />);
};

describe('DeckLegalities', () => {
  beforeEach(() => {
    // Setup default mock validator that returns no errors
    mockValidator.mockReturnValue({
      check: vi.fn().mockReturnValue([]),
    });
  });

  describe('when deck is legal in all formats', () => {
    it('renders the deck legalities container', () => {
      const { container } = renderDeckLegalities();
      expect(container.querySelector('.deck-legalities')).toBeInTheDocument();
    });

    it('displays legal status for each format', () => {
      renderDeckLegalities();
      
      Object.values(Legality).forEach((legality) => {
        const capitalizedLegality = legality.charAt(0).toUpperCase() + legality.slice(1);
        expect(screen.getByText(`Legal in ${capitalizedLegality}`)).toBeInTheDocument();
      });
    });

    it('does not show error styling when deck is legal', () => {
      const { container } = renderDeckLegalities();
      const legalityElements = container.querySelectorAll('.deck-legality');
      
      legalityElements.forEach((element) => {
        expect(element).not.toHaveClass('is-error');
      });
    });

    it('calls validator for each legality format', () => {
      const deck = create(FakeDeck);
      renderDeckLegalities({ deck, isBuilding: true });
      
      Object.values(Legality).forEach((legality) => {
        expect(mockValidator).toHaveBeenCalledWith(legality);
      });
    });
  });

  describe('when deck has legality errors', () => {
    beforeEach(() => {
      // Setup mock validator to return errors for some formats
      mockValidator.mockImplementation((legality: string) => ({
        check: vi.fn().mockReturnValue(
          legality === Legality.STANDARD 
            ? ['Too many copies of Lightning Bolt', 'Banned card: Black Lotus']
            : legality === Legality.MODERN
            ? ['Banned card: Skullclamp']
            : []
        ),
      }));
    });

    it('displays "Not legal" for formats with errors', () => {
      renderDeckLegalities();
      
      const standardCapitalized = Legality.STANDARD.charAt(0).toUpperCase() + Legality.STANDARD.slice(1);
      const modernCapitalized = Legality.MODERN.charAt(0).toUpperCase() + Legality.MODERN.slice(1);
      
      expect(screen.getByText(`Not legal in ${standardCapitalized}`)).toBeInTheDocument();
      expect(screen.getByText(`Not legal in ${modernCapitalized}`)).toBeInTheDocument();
    });

    it('shows error styling for formats with errors', () => {
      const { container } = renderDeckLegalities();
      const errorElements = container.querySelectorAll('.deck-legality.is-error');
      
      expect(errorElements.length).toBeGreaterThan(0);
    });

    it('displays specific error messages', () => {
      renderDeckLegalities();
      
      expect(screen.getByText('Too many copies of Lightning Bolt')).toBeInTheDocument();
      expect(screen.getByText('Banned card: Black Lotus')).toBeInTheDocument();
      expect(screen.getByText('Banned card: Skullclamp')).toBeInTheDocument();
    });

    it('renders error messages in error containers', () => {
      const { container } = renderDeckLegalities();
      const errorContainers = container.querySelectorAll('.deck-legality__errors');
      const errorMessages = container.querySelectorAll('.deck-legality__error');
      
      expect(errorContainers.length).toBeGreaterThan(0);
      expect(errorMessages.length).toBeGreaterThan(0);
    });
  });

  describe('when isBuilding prop changes', () => {
    it('passes isBuilding=true to validator', () => {
      const deck = create(FakeDeck);
      renderDeckLegalities({ deck, isBuilding: true });
      
      const mockCheck = mockValidator.mock.results[0]?.value?.check;
      expect(mockCheck).toHaveBeenCalledWith(deck, true);
    });

    it('passes isBuilding=false to validator', () => {
      const deck = create(FakeDeck);
      renderDeckLegalities({ deck, isBuilding: false });
      
      const mockCheck = mockValidator.mock.results[0]?.value?.check;
      expect(mockCheck).toHaveBeenCalledWith(deck, false);
    });
  });

  describe('when deck prop changes', () => {
    it('re-validates with new deck', () => {
      const initialDeck = create(FakeDeck, { name: 'Initial Deck' });
      const { rerender } = renderDeckLegalities({ deck: initialDeck });
      
      const newDeck = create(FakeDeck, { name: 'New Deck' });
      rerender(<DeckLegalities deck={newDeck} isBuilding={false} />);
      
      // Validator should be called again with the new deck
      const mockCheck = mockValidator.mock.results[0]?.value?.check;
      expect(mockCheck).toHaveBeenCalledWith(newDeck, false);
    });
  });

  describe('component structure', () => {
    it('renders correct number of legality items', () => {
      const { container } = renderDeckLegalities();
      const legalityItems = container.querySelectorAll('.deck-legality');
      
      expect(legalityItems).toHaveLength(Object.keys(Legality).length);
    });

    it('uses unique keys for legality items', () => {
      // This test ensures the component renders without React key warnings
      const { container } = renderDeckLegalities();
      const legalityItems = container.querySelectorAll('.deck-legality');
      
      expect(legalityItems.length).toBeGreaterThan(0);
    });
  });
});
