import { faker } from '@faker-js/faker';
import * as Immutable from 'immutable';
import { CardAttributes } from '../../src/models/CardAttributes';
import { CardInstance } from '../../src/models/CardInstances';
import { CardPanel, CardPanelType } from '../../src/models/CardPanels';
import { Card } from '../../src/models/Cards';
import { Condition } from '../../src/models/Condition';
import { Deck, DeckBoard, DeckCard } from '../../src/models/Decks';
import { Foil } from '../../src/models/Foil';
import { Game } from '../../src/models/Game';
import { Grouping } from '../../src/models/Grouping';
import { Language } from '../../src/models/Language';
import { Legality } from '../../src/models/Legality';
import { LorcanaCardInstance } from '../../src/models/lorcana/LorcanaCardInstances';
import { LorcanaCardGroup } from '../../src/models/lorcana/LorcanaCardPage';
import { LorcanaCard } from '../../src/models/lorcana/LorcanaCards';
import { LorcanaPrinting } from '../../src/models/lorcana/LorcanaPrinting';
import { MTGCardGroup, MTGCardPage } from '../../src/models/mtg/MTGCardPage';
import { MTGPrinting } from '../../src/models/mtg/MTGPrinting';
import { PokeCardInstance } from '../../src/models/pokemon/PokeCardInstances';
import { PokeCardGroup } from '../../src/models/pokemon/PokeCardPage';
import { PokeCard } from '../../src/models/pokemon/PokeCards';
import { PokePrinting } from '../../src/models/pokemon/PokePrinting';
import { PokeRarity } from '../../src/models/pokemon/PokeRarity';
import { Rarity } from '../../src/models/Rarity';
import { Sorting } from '../../src/models/sorting/Sorting';
import { Tag } from '../../src/models/Tags';
import { Viewing } from '../../src/models/Viewing';
import { YugiCardInstance } from '../../src/models/yugioh/YugiCardInstances';
import { YugiCardGroup } from '../../src/models/yugioh/YugiCardPage';
import { YugiCard } from '../../src/models/yugioh/YugiCards';
import { YugiPrinting } from '../../src/models/yugioh/YugiPrinting';
import { DisplayMode } from '../../src/views/components/cardpanel/DisplayImageSwitch';
import { create } from './Fake';
import { FakeMTGFilter } from './FakeFilters';
import { randomDate } from './FakeHelper';
import { IFake } from './FakeInterface';

export class FakeCardInstance implements IFake<CardInstance> {
  fake() {
    return new CardInstance({
      id: faker.number.int({ min: 0 }),
      uuid: faker.string.sample(),
      cardID: faker.number.int({ min: 0 }),
      foil: Foil.On,
      condition: faker.helpers.enumValue(Condition),
      language: faker.helpers.enumValue(Language),
      createdAt: randomDate(),
      foreignName: faker.string.sample(), // Potential improvement, look into non-Latin character generation.
      cardJsonID: faker.string.hexadecimal({ length: 32 }),
      // Limitation: faker.js currently has no way to limit amount of characters generated by lorem,
      // could potentially generate card names bigger than the longest card name in existence. Also biased against short names.
      cardName: faker.lorem.words(faker.number.int({ min: 1, max: 25 })),
      cardSetName: faker.lorem.words(faker.number.int({ min: 1, max: 25 })),
      cardSetCode: faker.string.sample(),
      cardCollectorNumber: faker.string.sample(),
      cardPrice: faker.number.int({ min: 100 }),
      cardManaCost: faker.string.sample(),
      cardTypes: Immutable.List([
        faker.string.sample(),
        faker.string.sample(),
        faker.string.sample(),
        faker.string.sample(),
      ]),
      cardSubTypes: Immutable.List([
        faker.string.sample(),
        faker.string.sample(),
        faker.string.sample(),
        faker.string.sample(),
      ]),
      cardRarity: faker.helpers.enumValue(Rarity),
      linkedResources: undefined, // I don't want to test deck functionality at this juncture as it is too complicated.
    });
  }
}

export class FakeCardInstanceWithOptions implements IFake<CardInstance> {
  fake(options?: Record<string, any>) {
    return new CardInstance({
      id: faker.number.int({ min: 0 }),
      uuid: faker.string.sample(),
      cardID: faker.number.int({ min: 0 }),
      foil: Foil.On,
      condition: faker.helpers.enumValue(Condition),
      language: faker.helpers.enumValue(Language),
      createdAt: randomDate(),
      foreignName: faker.string.sample(),
      cardJsonID: faker.string.hexadecimal({ length: 32 }),
      cardName: faker.lorem.words(faker.number.int({ min: 1, max: 25 })),
      cardSetName: faker.lorem.words(faker.number.int({ min: 1, max: 25 })),
      cardSetCode: faker.string.sample(),
      cardCollectorNumber: faker.string.sample(),
      cardPrice: options?.cardPrice || faker.number.int({ min: 100 }),
      cardManaCost: faker.string.sample(),
      cardTypes: Immutable.List([
        faker.string.sample(),
        faker.string.sample(),
        faker.string.sample(),
        faker.string.sample(),
      ]),
      cardSubTypes: Immutable.List([
        faker.string.sample(),
        faker.string.sample(),
        faker.string.sample(),
        faker.string.sample(),
      ]),
      cardRarity: faker.helpers.enumValue(Rarity),
      scannedImageURL: options?.scannedImageURL,
      scanMetadata: options?.scanMetadata,
    });
  }
}

export class FakePokeCardInstanceWithOptions implements IFake<PokeCardInstance> {
  fake(options?: Record<string, any>) {
    return new PokeCardInstance({
      uuid: faker.string.sample(),
      condition: faker.helpers.enumValue(Condition),
      card: new PokeCard({
        name: faker.string.sample(),
        rarity: faker.helpers.enumValue(PokeRarity),
      }),
      price: 0,
      inputSessionUUID: '',
      scannedImageURL: options?.scannedImageURL,
      scanMetadata: options?.scanMetadata,
    });
  }
}
export class FakeYugiCardInstanceWithOptions implements IFake<YugiCardInstance> {
  fake(options?: Record<string, any>) {
    return new YugiCardInstance({
      uuid: faker.string.sample(),
      condition: faker.helpers.enumValue(Condition),
      card: new YugiCard({
        name: faker.string.sample(),
      }),
      price: 0,
      inputSessionUUID: '',
      scannedImageURL: options?.scannedImageURL,
      scanMetadata: options?.scanMetadata,
    });
  }
}
export class FakeLorcanaCardInstanceWithOptions implements IFake<LorcanaCardInstance> {
  fake(options?: Record<string, any>) {
    return new LorcanaCardInstance({
      uuid: faker.string.sample(),
      condition: faker.helpers.enumValue(Condition),
      card: new LorcanaCard({
        name: faker.string.sample(),
      }),
      price: 0,
      inputSessionUUID: '',
      scannedImageURL: options?.scannedImageURL,
      scanMetadata: options?.scanMetadata,
    });
  }
}
export class FakeMTGCardGroup implements IFake<MTGCardGroup> {
  fake() {
    return new MTGCardGroup({
      cardInstanceLatest: randomDate(),
      cardInstanceCount: faker.number.int({ min: 0 }),
      cardInstancePrice: faker.number.int({ min: 0 }),
      cardInstances: Immutable.List<CardInstance>([create(FakeCardInstance)]),
      printingOptions: Immutable.List<MTGPrinting>(),
    });
  }
}
export class FakeMTGCardGroupWithOptions implements IFake<MTGCardGroup> {
  fake(options?: Record<string, any>) {
    return new MTGCardGroup({
      cardInstanceLatest: randomDate(),
      cardInstanceCount: faker.number.int({ min: 0 }),
      cardInstancePrice: faker.number.int({ min: 0 }),
      cardInstances: Immutable.List<CardInstance>(
        Array.from({ length: options?.cardInstancesLength || 1 }).map(() =>
          create(FakeCardInstanceWithOptions, options),
        ),
      ),
      printingOptions: Immutable.List<MTGPrinting>(),
    });
  }
}

export class FakePokeCardGroupWithOptions implements IFake<PokeCardGroup> {
  fake(options?: Record<string, any>) {
    return new PokeCardGroup({
      cardInstanceLatest: randomDate(),
      cardInstanceCount: faker.number.int({ min: 0 }),
      cardInstancePrice: faker.number.int({ min: 0 }),
      cardInstances: Immutable.List<PokeCardInstance>(
        Array.from({ length: options?.cardInstancesLength || 1 }).map(() =>
          create(FakePokeCardInstanceWithOptions, options),
        ),
      ),
      printingOptions: Immutable.List<PokePrinting>(),
    });
  }
}
export class FakeYugiCardGroupWithOptions implements IFake<YugiCardGroup> {
  fake(options?: Record<string, any>) {
    return new YugiCardGroup({
      cardInstanceLatest: randomDate(),
      cardInstanceCount: faker.number.int({ min: 0 }),
      cardInstancePrice: faker.number.int({ min: 0 }),
      cardInstances: Immutable.List<YugiCardInstance>(
        Array.from({ length: options?.cardInstancesLength || 1 }).map(() =>
          create(FakeYugiCardInstanceWithOptions, options),
        ),
      ),
      printingOptions: Immutable.List<YugiPrinting>(),
    });
  }
}

export class FakeLorcanaCardGroupWithOptions implements IFake<LorcanaCardGroup> {
  fake(options?: Record<string, any>) {
    return new LorcanaCardGroup({
      cardInstanceLatest: randomDate(),
      cardInstanceCount: faker.number.int({ min: 0 }),
      cardInstancePrice: faker.number.int({ min: 0 }),
      cardInstances: Immutable.List<LorcanaCardInstance>(
        Array.from({ length: options?.cardInstancesLength || 1 }).map(() =>
          create(FakeLorcanaCardInstanceWithOptions, options),
        ),
      ),
      printingOptions: Immutable.List<LorcanaPrinting>(),
    });
  }
}
export class FakeTag implements IFake<Tag> {
  fake() {
    return new Tag({
      id: faker.number.int({ min: 0 }),
      name: faker.string.sample(), // Put a limit on this when we implement character limits for tags.
      createdAt: randomDate(),
    });
  }
}

export class FakePrinting implements IFake<MTGPrinting> {
  fake() {
    return new MTGPrinting({
      jsonID: faker.string.hexadecimal({ length: 32 }),
      setCode: faker.string.sample(), // We could probably improve this.
      setName: faker.lorem.words(faker.number.int({ min: 1, max: 25 })),
      collectorNumber: faker.string.sample(),
      rarity: faker.helpers.enumValue(Rarity),
      languages: Immutable.Set<Language>(faker.helpers.enumValue(Language)), // most modern cards will have 11 languages availble to them.
    });
  }
}

export class FakeCardPanelWithOptions implements IFake<CardPanel> {
  fake(options?: Record<string, any>) {
    return new CardPanel({
      x: faker.number.int({ min: 0 }),
      y: typeof options?.y === 'number' ? options?.y : faker.number.int({ min: 0 }),
      game: options?.game || Game.MTG, // faker.helpers.enumValue(Game),
      cardGroup: options?.cardGroup || create(FakeMTGCardGroupWithOptions, options),
      tags: Immutable.OrderedSet<Tag>(Immutable.Iterable.Keyed([create(FakeTag)])),
      languageOptions: options?.languageOptions || Immutable.Set<Language>(faker.helpers.enumValue(Language)),
      printingOptionsReady: faker.datatype.boolean(),
      cardPanelType: options?.cardPanelType || faker.helpers.enumValue(CardPanelType),
      displayMode: options?.displayMode || DisplayMode.SOURCE,
      unsavedCardAttributes: options?.unsavedCardAttributes || new CardAttributes(),
    });
  }
}

export class FakeCardPanel implements IFake<CardPanel> {
  fake() {
    return new CardPanel({
      x: faker.number.int({ min: 0 }),
      y: faker.number.int({ min: 0 }),
      game: Game.MTG, // faker.helpers.enumValue(Game),
      cardGroup: create(FakeMTGCardGroup),
      tags: Immutable.OrderedSet<Tag>(Immutable.Iterable.Keyed([create(FakeTag)])),
      languageOptions: Immutable.Set<Language>(faker.helpers.enumValue(Language)),
      printingOptionsReady: faker.datatype.boolean(),
      cardPanelType: faker.helpers.enumValue(CardPanelType),
    });
  }
}

export class FakeMTGCardPage implements IFake<MTGCardPage> {
  fake() {
    return new MTGCardPage({
      ownerUsername: faker.string.sample(),
      totalCount: faker.number.int({ min: 0 }),
      totalGroupCount: faker.number.int({ min: 0 }),
      // Assuming following $.¢¢ notation (or regional equivalent).
      totalValue: faker.number.int({ min: 10 }),
      pageCount: faker.number.int({ min: 0 }),
      pageValue: faker.number.int({ min: 0 }),
      cardGroups: Immutable.List<MTGCardGroup>([create(FakeMTGCardGroup)]),
      cardGrouping: faker.helpers.enumValue(Grouping),
      cardSorting: faker.helpers.enumValue(Sorting),
      cardViewing: faker.helpers.enumValue(Viewing),
      filterOptions: create(FakeMTGFilter),
    });
  }
}

const fakeSuggestedCard = () => {
  return new Card({
    id: faker.number.int(),
    jsonID: faker.string.hexadecimal({ length: 32 }),
    name: faker.commerce.productName(),
    description: faker.lorem.paragraph(),
    setCode: faker.string.sample(),
    setName: faker.string.sample(),
    ownedCount: faker.number.int({ min: 0, max: 100 }),
  });
};

export const createFakeSuggestedCard = (count: number) => {
  return Array.from({ length: count }, (_, index) => {
    return fakeSuggestedCard();
  });
};

// Deck-related fake data utilities
export class FakeDeckCard implements IFake<DeckCard> {
  fake(options?: Record<string, any>) {
    return new DeckCard({
      id: options?.id || faker.number.int({ min: 1 }),
      boardId: options?.boardId || faker.number.int({ min: 1 }),
      cardId: options?.cardId || faker.number.int({ min: 1 }),
      cardInstanceId: options?.cardInstanceId || faker.number.int({ min: 1 }),
    });
  }
}

export class FakeDeckBoard implements IFake<DeckBoard> {
  fake(options?: Record<string, any>) {
    return new DeckBoard({
      id: options?.id || faker.number.int({ min: 1 }),
      name: options?.name || faker.lorem.words(2),
    });
  }
}

export class FakeDeck implements IFake<Deck> {
  fake(options?: Record<string, any>) {
    const boards =
      options?.boards ||
      Immutable.List([new DeckBoard({ id: 1, name: 'Main' }), new DeckBoard({ id: 2, name: 'Sideboard' })]);

    const cards = options?.cards || Immutable.Map<number, Card>();
    const deckCards = options?.deckCards || Immutable.Map<number, DeckCard>();

    return new Deck({
      dateEdited: options?.dateEdited || randomDate(),
      dateCreated: options?.dateCreated || randomDate(),
      id: options?.id || faker.number.int({ min: 1 }),
      userId: options?.userId || faker.number.int({ min: 1 }),
      uuid: options?.uuid || faker.string.uuid(),
      image: options?.image,
      name: options?.name || faker.lorem.words(3),
      legality: options?.legality || Legality.FREEFORM,
      isPublic: options?.isPublic || faker.datatype.boolean(),
      numCards: options?.numCards || faker.number.int({ min: 0, max: 100 }),
      price: options?.price || faker.number.int({ min: 0, max: 10000 }),
      boards,
      cards,
      deckCards,
      colorWhite: options?.colorWhite ?? faker.datatype.boolean(),
      colorBlue: options?.colorBlue ?? faker.datatype.boolean(),
      colorBlack: options?.colorBlack ?? faker.datatype.boolean(),
      colorRed: options?.colorRed ?? faker.datatype.boolean(),
      colorGreen: options?.colorGreen ?? faker.datatype.boolean(),
      tags: options?.tags || Immutable.Map<string, Tag>(),
    });
  }
}
